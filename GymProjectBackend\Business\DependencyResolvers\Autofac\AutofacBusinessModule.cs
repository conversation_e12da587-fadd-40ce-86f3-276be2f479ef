using Autofac;
using Autofac.Extras.DynamicProxy;
using Business.Abstract;
using Business.Concrete;
using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.DependencyResolvers.Autofac
{
    public class AutofacBusinessModule:Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            // DbContext kaydı - Scoped lifetime ile
            builder.Register(c =>
            {
                var configuration = c.Resolve<IConfiguration>();
                var optionsBuilder = new DbContextOptionsBuilder<GymContext>();

                // Environment anahtarını oku
                var environment = configuration["Environment"] ?? "dev";

                // Environment'a göre connection string'i al
                var connectionString = configuration[$"ConnectionStrings:{environment}"];

                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException($"'{environment}' environment'ı için connection string bulunamadı!");
                }

                optionsBuilder.UseSqlServer(connectionString);
                return new GymContext(optionsBuilder.Options);
            }).As<GymContext>().InstancePerLifetimeScope();

            builder.RegisterType<UserManager>().As<IUserService>().InstancePerLifetimeScope();
            // DAL - Transient (Memory leak önleme)
            builder.RegisterType<EfUserDal>().As<IUserDal>().InstancePerDependency();
            builder.RegisterType<CompanyManager>().As<ICompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyDal>().As<ICompanyDal>().InstancePerDependency();
            builder.RegisterType<CompanyAdressManager>().As<ICompanyAdressService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyAdressDal>().As<ICompanyAdressDal>().InstancePerDependency();
            builder.RegisterType<MemberManager>().As<IMemberService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberDal>().As<IMemberDal>().InstancePerDependency();
            builder.RegisterType<MembershipTypeManager>().As<IMembershipTypeService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipTypeDal>().As<IMembershiptypeDal>().InstancePerDependency();
            builder.RegisterType<MembershipManager>().As<IMembershipService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipDal>().As<IMembershipDal>().InstancePerDependency();
            builder.RegisterType<CompanyUserManager>().As<ICompanyUserService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyUserDal>().As<ICompanyUserDal>().InstancePerDependency();
            builder.RegisterType<PaymentManager>().As<IPaymentService>().InstancePerLifetimeScope();
            builder.RegisterType<EfPaymentDal>().As<IPaymentDal>().InstancePerDependency();
            builder.RegisterType<UserCompanyManager>().As<IUserCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserCompanyDal>().As<IUserCompanyDal>().InstancePerDependency();
            builder.RegisterType<UnifiedCompanyManager>().As<IUnifiedCompanyService>().InstancePerLifetimeScope();
            builder.RegisterType<EntryExitHistoryManager>().As<IEntryExitHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfEntryExitHistoryDal>().As<IEntryExitHistoryDal>().InstancePerDependency();
            builder.RegisterType<CityManager>().As<ICityService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCityDal>().As<ICityDal>().InstancePerDependency();
            builder.RegisterType<TownManager>().As<ITownService>().InstancePerLifetimeScope();
            builder.RegisterType<EfTownDal>().As<ITownDal>().InstancePerDependency();
            builder.RegisterType<AuthManager>().As<IAuthService>().InstancePerLifetimeScope();
            builder.RegisterType<JwtHelper>().As<ITokenHelper>().InstancePerLifetimeScope();
            builder.RegisterType<ProductManager>().As<IProductService>().InstancePerLifetimeScope();
            builder.RegisterType<EfProductDal>().As<IProductDal>().InstancePerDependency();
            builder.RegisterType<TransactionManager>().As<ITransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<EfTransactionDal>().As<ITransactionDal>().InstancePerDependency();
            builder.RegisterType<OperationClaimManager>().As<IOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<EfOperationClaimDal>().As<IOperationClaimDal>().InstancePerDependency();
            builder.RegisterType<UserOperationClaimManager>().As<IUserOperationClaimService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserOperationClaimDal>().As<IUserOperationClaimDal>().InstancePerDependency();
            builder.RegisterType<RemainingDebtManager>().As<IRemainingDebtService>().InstancePerLifetimeScope();
            builder.RegisterType<EfRemainingDebtDal>().As<IRemainingDebtDal>().InstancePerDependency();
            builder.RegisterType<EfDebtPaymentDal>().As<IDebtPaymentDal>().InstancePerDependency();
            builder.RegisterType<DebtPaymentManager>().As<IDebtPaymentService>().InstancePerLifetimeScope();
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().InstancePerLifetimeScope();

            // Multi-tenant Company Context
            builder.RegisterType<CompanyContext>().As<ICompanyContext>().InstancePerLifetimeScope();

            builder.RegisterType<UserDeviceManager>().As<IUserDeviceService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserDeviceDal>().As<IUserDeviceDal>().InstancePerDependency();
            builder.RegisterType<MembershipFreezeHistoryManager>().As<IMembershipFreezeHistoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMembershipFreezeHistoryDal>().As<IMembershipFreezeHistoryDal>().InstancePerDependency();
            builder.RegisterType<LicensePackageManager>().As<ILicensePackageService>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicensePackageDal>().As<ILicensePackageDal>().InstancePerDependency();
            builder.RegisterType<UserLicenseManager>().As<IUserLicenseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfUserLicenseDal>().As<IUserLicenseDal>().InstancePerDependency();
            builder.RegisterType<LicenseTransactionManager>().As<ILicenseTransactionService>().InstancePerLifetimeScope();
            builder.RegisterType<EfLicenseTransactionDal>().As<ILicenseTransactionDal>().InstancePerDependency();

            // Expense Service ve Dal Kayıtları
            builder.RegisterType<ExpenseManager>().As<IExpenseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfExpenseDal>().As<IExpenseDal>().InstancePerDependency();

            // File Service Kaydı
            builder.RegisterType<FileManager>().As<IFileService>().InstancePerLifetimeScope();

            // Profile Service Kaydı
            builder.RegisterType<ProfileManager>().As<IProfileService>().InstancePerLifetimeScope();

            // Advanced Rate Limit Service Kaydı
            builder.RegisterType<AdvancedRateLimitManager>().As<IAdvancedRateLimitService>().InstancePerLifetimeScope();

            // QR Code Encryption Service Kaydı
            builder.RegisterType<QrCodeEncryptionManager>().As<IQrCodeEncryptionService>().InstancePerLifetimeScope();

            // Cache Service Kaydı - Singleton (Enterprise performance)
            builder.RegisterType<CacheManager>().As<ICacheService>().SingleInstance();

            // Egzersiz Sistemi Service ve Dal Kayıtları
            builder.RegisterType<ExerciseCategoryManager>().As<IExerciseCategoryService>().InstancePerLifetimeScope();
            builder.RegisterType<EfExerciseCategoryDal>().As<IExerciseCategoryDal>().InstancePerDependency();
            builder.RegisterType<SystemExerciseManager>().As<ISystemExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfSystemExerciseDal>().As<ISystemExerciseDal>().InstancePerDependency();
            builder.RegisterType<CompanyExerciseManager>().As<ICompanyExerciseService>().InstancePerLifetimeScope();
            builder.RegisterType<EfCompanyExerciseDal>().As<ICompanyExerciseDal>().InstancePerDependency();

            // Antrenman Programı Sistemi
            builder.RegisterType<WorkoutProgramTemplateManager>().As<IWorkoutProgramTemplateService>().InstancePerLifetimeScope();
            builder.RegisterType<EfWorkoutProgramTemplateDal>().As<IWorkoutProgramTemplateDal>().InstancePerDependency();
            builder.RegisterType<EfWorkoutProgramDayDal>().As<IWorkoutProgramDayDal>().InstancePerDependency();
            builder.RegisterType<EfWorkoutProgramExerciseDal>().As<IWorkoutProgramExerciseDal>().InstancePerDependency();

            // Üye Program Atama Sistemi
            builder.RegisterType<MemberWorkoutProgramManager>().As<IMemberWorkoutProgramService>().InstancePerLifetimeScope();
            builder.RegisterType<EfMemberWorkoutProgramDal>().As<IMemberWorkoutProgramDal>().InstancePerDependency();

            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly).AsImplementedInterfaces()
                .EnableInterfaceInterceptors(new ProxyGenerationOptions()
                {
                    Selector = new AspectInterceptorSelector()
                }).InstancePerLifetimeScope();
        }
    }
}
